<template>
  <div class="udp-message-container">
    <div class="message-input">
      <!-- <input
        v-model="message"
        type="text"
        placeholder="请输入要发送的消息"
        @keyup.enter="sendMessage"
      >
      <button @click="sendMessage" :disabled="!message">发送</button> -->
      <button @click="sendText('start_shooting_capture')">开始</button>
      <button @click="sendText('next_shooting_capture')">下一个</button>
      <button @click="sendText('stop_shooting')">停止</button>
      <button @click="captureImageFromBox(1, 10, 10, 200, 200)">截图测试</button>
    </div>

    <div class="message-display">
      <div>接收到的消息</div>
      <div class="message-content" v-if="receivedMsg.content">
        <div>内容：{{ receivedMsg.content }}</div>
        <div class="timestamp">时间：{{ receivedMsg.timestamp }}</div>
      </div>
      <p v-else>暂无消息</p>
    </div>

    <!-- 添加Person组件 -->
    <person ref="personRef" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, inject } from 'vue'
// import { invoke } from '@tauri-apps/api/core'
// import { listen } from '@tauri-apps/api/event'
import Person from './person.vue'  // 导入Person组件
import { time } from 'console';

const personRef = ref(null);  // 添加对Person组件的引用
const message = ref('')
const receivedMsg = ref({
  content: '',
  timestamp: ''
})

// 使用inject获取父组件提供的changeStatus方法
const changeStatus = inject('changeStatus')

const sendText = async (value: string) => {
  message.value = value
  sendMessage()
}

setTimeout(() => {
  sendText('start_shooting_capture')
}, 1500);
setTimeout(() => {
  sendText('start_shooting_capture')
}, 20000);
// 在 sendMessage 函数中
const sendMessage = async () => {
  if (!message.value) return
  try {
    console.log('发送消息:', message.value)
    let result = await window.api?.sendUdpMessage(message.value)
    console.log(result)
    message.value = ''
  } catch (error) {
    console.error('发送消息失败:', error)
  }
}

let unlisten: (() => void) | undefined
let unlistenStatus: (() => void) | undefined

type status = 'idle' | 'success' | 'error' | 'start' | 'catch' | 'shooting' | 'discorver'
let now_status: status = 'idle'

// 修改changeStatus实现，直接调用inject获取的方法
// 删除原有的changeStatus定义，改为：
// 不再需要provide('changeStatus', changeStatus)，因为我们现在使用的是父组件提供的方法

let currentTrackingId = null;
let currentTrackingIdCopy = null;
let trackingStartTime = 0;
let faceSnapshots = []; // {currentTrackingId ,base64Img, filename, confidence, gender, age }
let faceSnapshotsCopy = []; //防重入数据冲突
let trackingTimeout = null;
let lastTrackedId = null;
// 在 onMounted 中添加状态更新事件监听
onMounted(() => {
  // 监听 UDP 消息事件
  window.electron?.ipcRenderer?.on('udp-message', async (_, data) => {
    receivedMsg.value = data
    const content = data.content;
    // 解析消息内容
    if (content.includes('coord:')) {
      // 检测到人脸坐标信息
      try {
        //let deepFaces = parseFacesData(content)
        // changeStatus('shooting')
      } catch (e) {
        console.error('解析faces_coord失败:', e);
      }
    } else if (content.includes('age:')) {
      try {
        let faceDatas = parseFacesData(content);
        if (faceDatas.length === 0) {
          console.warn('未检测到人脸数据');
          return;
        }

        let face = undefined;
        if (currentTrackingId !== null) {
          // 如果有正在追踪的 ID，使用该 ID 的人脸数据
          face = faceDatas.find(f => f.id === currentTrackingId);
        } else {
          // 否则使用第一个人脸数据
          face = faceDatas[0];
        }
        if (!face || face.id === undefined) {
          console.warn('未找到匹配的人脸数据');
          return;
        }
        const id = face.id;
        const box = face.face_box;
        const gender = face.gender === "male" ? 1 : 0;
        const age = parseInt(face.age);
        changeStatus('shooting')
        // 如果当前没有正在跟踪的 id，则初始化追踪
        if (currentTrackingId === null) {

          currentTrackingId = id;
          trackingStartTime = Date.now();
          faceSnapshots = [];

          //设置 10 秒追踪截止
          trackingTimeout = setTimeout(async () => {
            console.log(`人脸跟踪6秒结束 ID(${currentTrackingId})`);
            faceSnapshotsCopy = [...faceSnapshots]; // 复制当前追踪数据
            currentTrackingIdCopy = currentTrackingId; // 记录当前追踪 ID
            await finishTracking();
          }, 6000);

          console.log(`开始追踪人脸 ID: ${id}`);
        }
        if (id === currentTrackingId) {
          // 每次收到都更新追踪状态
          const { success, base64Img, filename, confidence } = await captureImageFromBox(id, box[0], box[1], box[2], box[3]);
          console.log(`裁切人脸 ${success},置信度: ${filename},置信度: ${confidence}`);
          if (success) {
            faceSnapshots.push({ id ,base64Img, filename, confidence, gender, age });
            //console.log(`保存人脸 ${filename},置信度: ${confidence} 性别:${genger}_ ${face.gender}`);

            if( confidence >0.94 ){
              console.log(`人脸置信度(${confidence})大于0.94，提前结束追踪`);
              faceSnapshotsCopy = [...faceSnapshots]; // 复制当前追踪数据
              currentTrackingIdCopy = currentTrackingId; // 记录当前追踪 ID
              await finishTracking();
            } else {
              console.log(`人脸置信度(${confidence})小于0.94，继续追踪`);
            }
          }
        } else {
          // 收到的是新 ID，提前终止追踪当前 ID
          console.log(`检测到不同的人脸 ID(${id})，终止当前追踪 ID(${currentTrackingId})`);
          faceSnapshotsCopy = [...faceSnapshots]; // 复制当前追踪数据
          currentTrackingIdCopy = currentTrackingId; // 记录当前追踪 ID
          await finishTracking();
        }
      } catch (e) {
        console.error('解析人脸数据失败:', e);
      }
    }
  })

  // 添加状态更新事件监听 - 修复：移到 UDP 消息监听器外部
  window.electron?.ipcRenderer?.on('status-update', (_, status) => {
    changeStatus(status)
  })
})

async function finishTracking() {
  currentTrackingId = null;
  faceSnapshots = [];
  console.log(`当前人脸追拍完成${faceSnapshotsCopy.length}张图像`);
  if (trackingTimeout !== null) {
    clearTimeout(trackingTimeout);
    console.log('追踪结束，清理定时器');
  } else {
    console.warn('没有正在进行的追踪');
  }

  trackingTimeout = null;

  let best = null;
  if (faceSnapshotsCopy.length > 0) {
    lastTrackedId = currentTrackingIdCopy;
    faceSnapshotsCopy.sort((a, b) => b.confidence - a.confidence);
    best = faceSnapshotsCopy[0];
    console.log('追拍人脸排序后:', faceSnapshotsCopy.map(s => s.confidence));
    console.log(`人脸选取最佳图像: ${best.filename}（置信度: ${best.confidence}）`);
  } else {
    console.warn('未保存任何图像');
  }
  console.log(`设置人脸数据:${best}`);
  if (best !== null) {
    changeStatus('catch')
    await window.api.greet({
      id: currentTrackingIdCopy,
      time: formatTimestamp(),
      img: best.base64Img,
      gender: best.gender,
      age: best.age
    });
  }
  sendText('next_shooting_capture');
  currentTrackingId = null;
  faceSnapshots = [];
}

function parseFacesData(input) {
  const records = input.split('],').map(s => s.trim());
  const results = [];

  for (let record of records) {
    if (!record.endsWith(']')) {
      record += ']';  // 补全 ]
    }
    results.push(parseFaceData(record));
  }

  return results;
}

function parseFaceData(str) {
  const result = {};
  const content = str.slice(1, -1);  // 去掉 []
  const tokens = content.split(',');

  let currentKey = null;

  for (const token of tokens) {
    if (token.includes(':')) {
      const [key, value] = token.split(':');
      currentKey = key.trim();
      const parsed = parseValue(value.trim());
      result[currentKey] = parsed instanceof Array ? [parsed] : parsed;
    } else if (currentKey) {
      const parsed = parseValue(token.trim());
      if (!(result[currentKey] instanceof Array)) {
        // 把单值转为数组
        result[currentKey] = [result[currentKey]];
      }
      result[currentKey].push(parsed);
    }
  }

  return result;
}

function parseValue(val) {
  if (!isNaN(val)) {
    return parseFloat(val);
  } else {
    return val;  // 保留字符串
  }
}

function formatTimestamp() {
  const now = new Date();

  const pad = (n, len = 2) => n.toString().padStart(len, '0');

  const year = now.getFullYear();
  const month = pad(now.getMonth() + 1);      // 月份从0开始
  const day = pad(now.getDate());
  const hour = pad(now.getHours());
  const minute = pad(now.getMinutes());
  const second = pad(now.getSeconds());
  const millisecond = pad(now.getMilliseconds(), 3);  // 保留3位

  return `${year}${month}${day}${hour}${minute}${second}${millisecond}`;
}

// 根据人脸框坐标从摄像头截图的函数
// 修改截图函数
const captureImageFromBox = async (id: number, x1: number, y1: number, x2: number, y2: number) => {
  if (personRef.value) {
    // 获取图片
    const base64Image = (personRef.value as any).captureImageFromBox(x1, y1, x2, y2);
    if (base64Image) {
      // 检测是否包含人脸
      let { success: _hasFace, confidence: _confidence } = await (personRef.value as any).testFace(base64Image);
      if (_hasFace) {
        console.log('检测到人脸，保存图片');
        // 发送到主进程保存
        try {
          const timestamp = `${id}_${formatTimestamp()}_${_confidence}`;//String(new Date().getTime());
          const _filename = `face_${timestamp}.jpg`;
          // await window.api.saveFaceImage({
          //   filename: _filename,
          //   data: base64Image
          // })
          // console.log('图片保存成功');
          return {
            success: _hasFace,
            base64Img: base64Image,
            filename: _filename,
            confidence: _confidence 
          };
        } catch (error) {
          console.error('保存图片失败:', error);
        }
      } else {
        console.log('未检测到人脸，跳过保存');
      }
    }
  }
  return {
    success: false,
    base64Img: '',
    filename: '',
    confidence: 0
  };
};
onUnmounted(() => {
  // 移除事件监听器
  window.electron?.ipcRenderer?.removeAllListeners('udp-message');
  window.electron?.ipcRenderer?.removeAllListeners('status-update');
})
</script>

<style scoped>
.udp-message-container {
  padding: 20px;
  max-width: 600px;
  margin: 0 auto;
}

.message-input {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.message-input input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.message-input button {
  padding: 8px 16px;
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.message-input button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.message-display {
  padding: 5px;
  border: 1px solid #eee;
  border-radius: 4px;
}

.message-content {
  background-color: #f9f9f9;
  padding: 2px;
  border-radius: 4px;
}

.timestamp {
  color: #666;
  font-size: 0.9em;
}
</style>
